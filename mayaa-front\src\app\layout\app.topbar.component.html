<div class="layout-topbar">
  <div class="layout-topbar-start">
    <a class="layout-topbar-logo" routerLink="/">
      @if (topBarConfig.logoDark || topBarConfig.logoLight) {
      <img style="display:var(--display-logo-light)" [src]="topBarConfig.logoLight" height="45" />
      <img style="display:var(--display-logo-dark)" [src]="topBarConfig.logoDark" height="45" />
      }
      @else {
      <img style="display:var(--display-logo-light)" src="assets/layout/images/logo-sh/logo_white.png" height="45" />
      <img style="display:var(--display-logo-dark);filter: grayscale(1) contrast(100%) brightness(15%);"
        src="assets/layout/images/logo-sh/logo_white.png" width="45" height="45" />
      }
      @if (topBarConfig.appName) {
      <h4 class="ml-3" style="color:var(--topbar-item-text-color)">{{topBarConfig.appName}}</h4>
      }
      @else {
      <h4 class="ml-3" style="color:var(--topbar-item-text-color)">My LQS App</h4>
      }

    </a>
    <a #menuButton class="layout-menu-button" (click)="onMenuButtonClick()" pRipple>
      <i class="pi pi-chevron-right"></i>
    </a>

    <a #mobileMenuButton class="layout-topbar-mobile-button" (click)="onMobileTopbarMenuButtonClick()" pRipple>
      <i class="pi pi-ellipsis-v"></i>
    </a>
  </div>

  <div class="layout-topbar-end">
    <div class="layout-topbar-actions-start">
      <div class="p-d-flex p-ai-center">
        <!-- Sélection de l'unité -->
        <p-dropdown [(ngModel)]="selectedUnite" [options]="uniteList" class="mr-1 dynamic-width"
          (ngModelChange)="onUniteChange($event)"></p-dropdown>

        <!-- Bouton de débogage temporaire -->
        <button type="button" class="p-button p-button-sm p-button-outlined"
                (click)="debugUniteFiltering()"
                title="Debug filtrage unités">
          🔍 Debug
        </button>

        <!-- Sélection du mois/année avec une largeur réduite -->
        <p-calendar [(ngModel)]="selectedMonthYear" view="month" (ngModelChange)="onMonthYearChange($event)"
          dateFormat="mm/yy" placeholder="mois/année" styleClass="small-calendar"></p-calendar>

        <!-- Tag cliquable pour ouvrir le dialogue -->
        <p-tag [severity]="dataStatus === 'upToDate' ? 'success' : dataStatus === 'changed' ? 'warning' : 'danger'"
          [value]="dataStatus === 'upToDate' ? 'À Jour' : dataStatus === 'changed' ? 'Données modifiées' : 'Non chargé'"
          [rounded]="true" (click)="showDiffDialog()" class="cursor-pointer p-tag-hover">
        </p-tag>
      </div>
      <p-toast />
      <p-confirmDialog />
      <!-- Dialog affichant les différences -->
      <p-dialog [(visible)]="diffDialogVisible" header="Différences de données" [modal]="true" [closable]="true">
        <p-table [value]="dataRows">
          <ng-template pTemplate="header">
            <tr>
              <th>Libellé</th>
              <th>Données chargées</th>
              <th>Données SISPRO</th>
            </tr>
          </ng-template>
          <ng-template pTemplate="body" let-row>
            <tr>
              <td>{{ row.field }}</td>
              <td>{{ row.loaded }}</td>
              <td>{{ row.sispro }}</td>
            </tr>
          </ng-template>
        </p-table>

        <div class="p-d-flex p-jc-end p-mt-3">
          <p-button label="Mettre à jour" [disabled]="!hasDifference" icon="pi pi-check" severity="success"
            (click)="confirmRefresh(selectedMonthYear!,1)"></p-button>
        </div>
      </p-dialog>





    </div>
    <div class="layout-topbar-actions-end">
      <ul class="layout-topbar-items">
        @if (topBarConfig.switchLightDark) {
        <div class="tdnn" (click)="toggleDarkTheme()" [ngClass]="{ 'day': layoutService.config().colorScheme=='light'}">
          <div class="moon" [ngClass]="{ 'sun': layoutService.config().colorScheme=='light' }"></div>
        </div>
        }
        @if (topBarConfig.searchBar) {
        <li class="layout-topbar-search">
          <a pStyleClass="@next" enterFromClass="ng-hidden" enterActiveClass="px-scalein" leaveToClass="ng-hidden"
            leaveActiveClass="px-fadeout" [hideOnOutsideClick]="true" pRipple (click)="focusSearchInput()">
            <i class="pi pi-search"></i>
          </a>

          <div class="layout-search-panel ng-hidden p-input-filled">
            <i class="pi pi-search"></i>
            <input #searchInput type="text" pInputText placeholder="Search">
            <button pButton pRipple type="button" icon="pi pi-times"
              class="p-button-rounded p-button-text p-button-plain" pStyleClass=".layout-search-panel"
              leaveToClass="ng-hidden" leaveActiveClass="px-fadeout"></button>
          </div>
        </li>
        }
        @if (topBarConfig.notifications) {
        <li>
          <a pStyleClass="@next" enterFromClass="ng-hidden" enterActiveClass="px-scalein" leaveToClass="ng-hidden"
            leaveActiveClass="px-fadeout" [hideOnOutsideClick]="true" pRipple>
            <i class="pi pi-bell" pBadge severity="warning"></i>
          </a>
          <div class="ng-hidden">
            <ul class="list-none p-0 m-0">
              <li class="px-3 py-1">
                @switch (topBarConfig.notificationsList!.length) {
                @case (1) {
                <span>Vous avez <b>{{topBarConfig.notificationsList!.length}}</b> nouvelle notifications</span>
                }
                @default {
                <span>Vous avez <b>{{topBarConfig.notificationsList!.length}}</b> nouvelles notifications</span>
                }
                }
              </li>
              @for (notif of topBarConfig.notificationsList!; track notif.title+notif.details) {
              <li class="p-3">
                <div class="flex align-items-center">
                  @switch (notif.image) {
                  @case (undefined) {
                  <i class="pi {{notif.icon}}" style="font-size: 2rem"></i>
                  }
                  @default {
                  <img [src]="notif.image!" />
                  }
                  }
                  <div class="flex flex-column ml-3 flex-1">
                    <div class="flex align-items-center justify-content-between mb-1">
                      <span class="font-bold">{{notif.title}}</span>
                      <small>{{notif.time | date:'dd-MM-yyyy' }}</small>
                    </div>
                    <span class="text-sm line-height-3">{{notif.details}}</span>
                  </div>
                </div>
              </li>
              }
            </ul>
          </div>
        </li>
        }
        @if (topBarConfig.showApps) {
        <li>
          <a pStyleClass="@next" enterFromClass="ng-hidden" enterActiveClass="px-scalein" leaveToClass="ng-hidden"
            leaveActiveClass="px-fadeout" [hideOnOutsideClick]="true" pRipple>
            <i class="pi pi-table"></i>
          </a>
          <div class="ng-hidden">
            <div class="flex flex-wrap">
              @for (app of topBarConfig.appsList!; track app.name+app.icon) {
              <div class="w-4 flex flex-column align-items-center p-3">
                <button pButton class="p-button-rounded mb-2 p-button-{{app.bgcolor}}" icon="pi {{app.icon}}"
                  pRipple></button>
                <span>{{app.name}}</span>
              </div>
              }
            </div>
          </div>
        </li>
        }
        <li>
          <a pStyleClass="@next" enterFromClass="ng-hidden" enterActiveClass="px-scalein" leaveToClass="ng-hidden"
            leaveActiveClass="px-fadeout" [hideOnOutsideClick]="true" pRipple>
            <img [src]="topBarConfig.userAvatar? topBarConfig.userAvatar :'assets/layout/images/avatars/avatar1.svg'"
              alt="avatar" class="w-2rem h-2rem">
          </a>
          <div class="ng-hidden">
            <ul class="list-none p-0 m-0">
              @for (item of topBarConfig.userMenu!; track item.label) {
              <li>
                <a (click)="item.command" [routerLink]="item.routerLink"
                  class="cursor-pointer flex align-items-center py-2 hover:surface-hover transition-duration-150 transition-all px-3 py-2"
                  pRipple>
                  <i class="pi {{item.icon}} mr-2"></i>
                  <span>{{item.label}}</span>
                </a>
              </li>
              }

              <li>
                <span
                  class="flex align-items-center py-2 px-3 text-sm text-color-secondary surface-border">
                  <i class="pi pi-user mr-2"></i>
                  <span>{{userConnected().prenom}}</span>
                </span>
              </li>
              <li>
                <span class="flex align-items-center py-2 px-3 text-sm text-color-secondary border-bottom-1 surface-border">

                  <span>{{userConnected().fonction}}</span>
                </span>
              </li>
              <li>
                <a (click)="onLogout()"
                  class="cursor-pointer flex align-items-center py-2 hover:surface-hover transition-duration-150 transition-all px-3 py-2"
                  pRipple>
                  <i class="pi pi-power-off mr-2"></i>
                  <span>Se déconnecter</span>
                </a>
              </li>
            </ul>
          </div>

        </li>

        @if (topBarConfig.showRightMneu) {
        <li>
          <a (click)="onRightMenuButtonClick()" pRipple>
            <i class="pi pi-arrow-left"></i>
          </a>
        </li>
        }
      </ul>
    </div>
  </div>
</div>
