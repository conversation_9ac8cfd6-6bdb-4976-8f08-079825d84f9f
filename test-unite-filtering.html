<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Filtrage Unités par Groupes Keycloak</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 800px; margin: 0 auto; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .result { margin: 10px 0; padding: 10px; background-color: #f5f5f5; border-radius: 3px; }
        .error { background-color: #ffebee; color: #c62828; }
        .success { background-color: #e8f5e8; color: #2e7d32; }
        button { padding: 10px 15px; margin: 5px; background-color: #007bff; color: white; border: none; border-radius: 3px; cursor: pointer; }
        button:hover { background-color: #0056b3; }
        input { padding: 8px; margin: 5px; border: 1px solid #ddd; border-radius: 3px; width: 200px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>Test Filtrage Unités par Groupes Keycloak</h1>
        
        <div class="test-section">
            <h3>1. Test récupération de toutes les unités M_UNITE</h3>
            <button onclick="testGetAllMUnites()">Tester GET /api/m-unites</button>
            <div id="result-all-unites" class="result"></div>
        </div>

        <div class="test-section">
            <h3>2. Test filtrage par groupes Keycloak</h3>
            <p>Entrez les IDs de groupes Keycloak (séparés par des virgules) :</p>
            <input type="text" id="group-ids" placeholder="ex: 6ac06a84-5fd3-4d1e-9a43-abdddddbbabe1,EXP" />
            <button onclick="testFilterByGroups()">Tester POST /api/m-unites/by-groups</button>
            <div id="result-filter-groups" class="result"></div>
        </div>

        <div class="test-section">
            <h3>3. Test avec groupe EXP (accès à toutes les unités, mais EXP exclu du dropdown)</h3>
            <button onclick="testExpGroup()">Tester avec groupe EXP</button>
            <div id="result-exp-group" class="result"></div>
        </div>

        <div class="test-section">
            <h3>4. Test avec groupes spécifiques</h3>
            <button onclick="testSpecificGroups()">Tester avec groupes spécifiques</button>
            <div id="result-specific-groups" class="result"></div>
        </div>
    </div>

    <script>
        const API_BASE_URL = 'http://localhost:8080/api';

        async function testGetAllMUnites() {
            const resultDiv = document.getElementById('result-all-unites');
            try {
                const response = await fetch(`${API_BASE_URL}/m-unites`);
                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.className = 'result success';
                    resultDiv.innerHTML = `
                        <strong>Succès !</strong><br>
                        Nombre d'unités trouvées: ${data.length}<br>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.innerHTML = `<strong>Erreur:</strong> ${response.status} - ${response.statusText}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `<strong>Erreur:</strong> ${error.message}`;
            }
        }

        async function testFilterByGroups() {
            const resultDiv = document.getElementById('result-filter-groups');
            const groupIdsInput = document.getElementById('group-ids').value;
            
            if (!groupIdsInput.trim()) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = '<strong>Erreur:</strong> Veuillez entrer des IDs de groupes';
                return;
            }

            const groupIds = groupIdsInput.split(',').map(id => id.trim());
            
            try {
                const response = await fetch(`${API_BASE_URL}/m-unites/by-groups`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(groupIds)
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.className = 'result success';
                    resultDiv.innerHTML = `
                        <strong>Succès !</strong><br>
                        Groupes testés: ${groupIds.join(', ')}<br>
                        Nombre d'unités accessibles: ${data.length}<br>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.innerHTML = `<strong>Erreur:</strong> ${response.status} - ${response.statusText}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `<strong>Erreur:</strong> ${error.message}`;
            }
        }

        async function testExpGroup() {
            const resultDiv = document.getElementById('result-exp-group');
            const groupIds = ['EXP'];
            
            try {
                const response = await fetch(`${API_BASE_URL}/m-unites/by-groups`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(groupIds)
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.className = 'result success';
                    resultDiv.innerHTML = `
                        <strong>Succès !</strong><br>
                        Groupe EXP testé - Doit retourner toutes les unités SAUF EXP lui-même<br>
                        Nombre d'unités accessibles: ${data.length}<br>
                        Vérification: Aucune unité avec keycloakGroupId="EXP" ne doit apparaître<br>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.innerHTML = `<strong>Erreur:</strong> ${response.status} - ${response.statusText}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `<strong>Erreur:</strong> ${error.message}`;
            }
        }

        async function testSpecificGroups() {
            const resultDiv = document.getElementById('result-specific-groups');
            // Utiliser les IDs de groupes de l'exemple fourni
            const groupIds = ['6ac06a84-5fd3-4d1e-9a43-abdddddbbabe1', 'e8228ead-f960-466a-8eb1-cad0228136e0'];
            
            try {
                const response = await fetch(`${API_BASE_URL}/m-unites/by-groups`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(groupIds)
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.className = 'result success';
                    resultDiv.innerHTML = `
                        <strong>Succès !</strong><br>
                        Groupes testés: ${groupIds.join(', ')}<br>
                        Nombre d'unités accessibles: ${data.length}<br>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.innerHTML = `<strong>Erreur:</strong> ${response.status} - ${response.statusText}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `<strong>Erreur:</strong> ${error.message}`;
            }
        }
    </script>
</body>
</html>
