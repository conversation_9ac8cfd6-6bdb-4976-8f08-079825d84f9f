package dz.sonatrach.weblqs.mayaaback.model;

import com.fasterxml.jackson.annotation.JsonView;
import dz.sonatrach.weblqs.mayaaback.views.View;
import jakarta.persistence.*;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.io.Serializable;

/**
 * Entité représentant la table M_UNITE
 * Contient les informations sur les unités avec leurs groupes Keycloak associés
 */
@Entity
@Table(name = "M_UNITE")
@NamedQuery(name = "MUnite.findAll", query = "SELECT m FROM MUnite m")
@EqualsAndHashCode(onlyExplicitlyIncluded = true)
@ToString
public class MUnite implements Serializable {
    private static final long serialVersionUID = 1L;

    @Id
    @Column(name = "ID_UNITE")
    @JsonView(View.basic.class)
    private Long idUnite;

    @Column(name = "DESC_UNITE")
    @JsonView(View.basic.class)
    private String descUnite;

    @Column(name = "CODE_UNITE")
    @JsonView(View.basic.class)
    private String codeUnite;

    @Column(name = "K_ID_GROUP")
    @JsonView(View.basic.class)
    private String keycloakGroupId;

    // Constructeurs
    public MUnite() {}

    public MUnite(Long idUnite, String descUnite, String codeUnite, String keycloakGroupId) {
        this.idUnite = idUnite;
        this.descUnite = descUnite;
        this.codeUnite = codeUnite;
        this.keycloakGroupId = keycloakGroupId;
    }

    // Getters et Setters
    public Long getIdUnite() {
        return idUnite;
    }

    public void setIdUnite(Long idUnite) {
        this.idUnite = idUnite;
    }

    public String getDescUnite() {
        return descUnite;
    }

    public void setDescUnite(String descUnite) {
        this.descUnite = descUnite;
    }

    public String getCodeUnite() {
        return codeUnite;
    }

    public void setCodeUnite(String codeUnite) {
        this.codeUnite = codeUnite;
    }

    public String getKeycloakGroupId() {
        return keycloakGroupId;
    }

    public void setKeycloakGroupId(String keycloakGroupId) {
        this.keycloakGroupId = keycloakGroupId;
    }
}
