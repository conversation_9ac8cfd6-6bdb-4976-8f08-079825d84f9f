package dz.sonatrach.weblqs.mayaaback.repo;

import dz.sonatrach.weblqs.mayaaback.model.ConsommationEnergetique;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

/**
 * Repository pour l'entité ConsommationEnergetique
 */
@Repository
public interface ConsommationEnergetiqueRepository extends JpaRepository<ConsommationEnergetique, Long> {
    
    /**
     * Trouve les données de consommation énergétique pour une unité et un mois donnés
     * @param unite Code de l'unité
     * @param mois Mois de référence
     * @return Optional contenant les données de consommation énergétique
     */
    Optional<ConsommationEnergetique> findByUniteAndMois(String unite, LocalDate mois);
    
    /**
     * Trouve toutes les données de consommation énergétique pour un mois donné
     * @param mois Mois de référence
     * @return Liste des données de consommation énergétique
     */
    List<ConsommationEnergetique> findByMois(LocalDate mois);
    
    /**
     * Trouve toutes les données de consommation énergétique pour une unité donnée
     * @param unite Code de l'unité
     * @return Liste des données de consommation énergétique
     */
    List<ConsommationEnergetique> findByUnite(String unite);
    
    /**
     * Trouve toutes les données de consommation énergétique pour une unité donnée, triées par mois décroissant
     * @param unite Code de l'unité
     * @return Liste des données de consommation énergétique triées par mois décroissant
     */
    List<ConsommationEnergetique> findByUniteOrderByMoisDesc(String unite);
}
