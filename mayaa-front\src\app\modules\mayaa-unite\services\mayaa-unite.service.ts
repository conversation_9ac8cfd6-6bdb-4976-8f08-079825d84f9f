import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { environment } from '../../../environments/environment';
import { catchError, map, Observable, of, throwError, forkJoin } from 'rxjs';
import { CacheService } from '../../../services/cache.service';
import { TestDataComplexe } from '../../../model/TestDataComplexe';
import { AutoConsMens } from '../../../model/AutoConsMens';
import {
  AutoconsommationRecap,
  AutoconsommationData,
} from '../../../model/AutoconsommationData';
import { DetailMensuel } from '../../../model/ConsommationDetail';
import { BilanGlobalComplexe } from '../../../model/BilanGlobalComplexe';
import { GazTorcheeParCauseTorchage } from '../../../model/GazTorcheeParCauseTorchage';
import { UtilisationChaudieres } from '../../../model/UtilisationChaudieres';
import { GazTorchDetails } from '../../../model/GazTorchDetails';
import { IndicateurPerformanceUnite } from '../../../model/IndicateurPerformanceUnite';
import { ListUnite } from '../../../model/ListUnite';
@Injectable({
  providedIn: 'root',
})
export class MayaaUniteService {
  private baseUrl: string = environment.apiUrl + '/api';
  private readonly localUrl = 'assets/json/bilanglobal.json';
  constructor(private http: HttpClient, private cacheService: CacheService) {}
  /**
   * Appelle le endpoint GET pour charger les données (exécuter la procédure).
   * URL construite : /api/unite/load/{mois}/{annee}/{unite}
   *
   * @param mois   Le mois sous forme de chaîne (ex: "10")
   * @param annee  L'année sous forme de chaîne (ex: "2024")
   * @param unite  L'unité (ex: "5X2")
   * @returns      Un Observable<void> indiquant le succès de l'opération
   */
  loadAutoCons(mois: string, annee: string, unite: string): Observable<void> {
    const url = `${this.baseUrl}/load/${mois}/${annee}/${unite}`;
    return this.http.get<void>(url);
  }

  /**
   * Appelle le endpoint GET pour vérifier les données de la vue TEST_DATA_COMPLEXE.
   * URL construite : /api/status/{unite}/{pmois}
   *
   * @param mois   Le mois sous forme de chaîne (ex: "10")
   * @param annee  L'année sous forme de chaîne (ex: "2024")
   * @param unite  L'unité (ex: "5X2")
   * @returns      Un Observable<TestDataComplexe> contenant les données récupérées
   */
  verifDataComplexe(
    mois: string,
    annee: string,
    unite: string
  ): Observable<TestDataComplexe> {
    // Convertir mois/annee vers le format pmois ddMMyyyy
    const pmois = `01${mois.padStart(2, '0')}${annee}`;
    const url = `${this.baseUrl}/status/${unite}/${pmois}`;
    return this.http.get<TestDataComplexe>(url);
  }

  /**
   * Récupère le dernier enregistrement pour l'unité donnée et retourne la date pmois.
   * Exemple d'URL appelée : GET {baseUrl}/lastmonth/5X2
   */
  getLastPMois(unite: string): Observable<Date> {
    const url = `${this.baseUrl}/auto-cons-mens/latest/${unite}`;
    return this.http.get<AutoConsMens>(url).pipe(
      map((record: AutoConsMens) => {
        if (record.pmois) {
          return new Date(record.pmois);
        } else {
          throw new Error('La propriété pmois est indéfinie');
        }
      })
    );
  }
  getBilanUnite(
    mois: string,
    annee: string,
    unite: string
  ): Observable<AutoConsMens> {
    const url = `${this.baseUrl}/auto-cons-mens/01${mois}${annee}/${unite}`;
    return this.http.get<AutoConsMens>(url);
  }

  /**
   * Récupère les données d'autoconsommation réelles depuis les APIs (avec cache)
   * @param unite Code de l'unité (ex: "5X2")
   * @param pmois Période au format ddMMyyyy (ex: "01122024")
   * @returns Observable contenant les données d'autoconsommation
   */
  getAutoconsommationData(unite: string, pmois: string): Observable<AutoconsommationRecap> {
    const cacheKey = this.cacheService.generateKey('autoconsommation', unite, pmois);

    return this.cacheService.getOrSet(cacheKey, () => {
      // Appels parallèles aux APIs
      const autoConsUrl = `${this.baseUrl}/auto-cons-mens/${pmois}/${unite}`;
      const gazTorchesUrl = `${this.baseUrl}/gaz-torchee-par-cause-torchage/${unite}/${pmois}`;

      return forkJoin({
        autoCons: this.http.get<AutoConsMens>(autoConsUrl),
        gazTorches: this.http.get<GazTorcheeParCauseTorchage[]>(gazTorchesUrl).pipe(
          catchError(() => of([])) // Si pas de données gaz torchés, retourner tableau vide
        )
    }).pipe(
      map(({ autoCons, gazTorches }) => {
        // Calculer le taux de consommation nette
        const tauxConsommationNette = autoCons.autoConsMoisNet && autoCons.receptionGnMois
          ? (autoCons.autoConsMoisNet * 100) / autoCons.receptionGnMois
          : 0;

        // Séparer les gaz torchés par source (interne/externe)
        const gazTorchesInterne = gazTorches
          .filter(item => item.codeCauseTorchage?.startsWith('I') || item.libCauseTorchage?.toLowerCase().includes('interne'))
          .reduce((sum, item) => sum + (item.quantiteGazTorchee || 0), 0);

        const gazTorchesExterne = gazTorches
          .filter(item => item.codeCauseTorchage?.startsWith('E') || item.libCauseTorchage?.toLowerCase().includes('externe'))
          .reduce((sum, item) => sum + (item.quantiteGazTorchee || 0), 0);

        // Calculer les taux de gaz torchés
        const tauxGazTorchesInterne = autoCons.receptionGnMois
          ? (gazTorchesInterne * 100) / autoCons.receptionGnMois
          : 0;

        const tauxGazTorchesExterne = autoCons.receptionGnMois
          ? (gazTorchesExterne * 100) / autoCons.receptionGnMois
          : 0;

        // Extraire mois et année de pmois (format ddMMyyyy)
        const date = new Date(
          parseInt(pmois.substring(4, 8)), // année
          parseInt(pmois.substring(2, 4)) - 1, // mois (0-indexé)
          parseInt(pmois.substring(0, 2)) // jour
        );
        const moisNom = date.toLocaleDateString('fr-FR', { month: 'long' });
        const annee = date.getFullYear().toString();

        const data: AutoconsommationData[] = [
          {
            label: 'Consommation nette',
            cm3GN: autoCons.autoConsMoisNet || 0,
            pourcentageGN: tauxConsommationNette,
            category: 'consommation',
            // Pas de sourceType car l'autoconsommation nette est globale
          }
        ];

        // Ajouter les gaz torchés seulement s'il y en a
        if (gazTorchesInterne > 0) {
          data.push({
            label: 'Gaz Torchés interne',
            cm3GN: gazTorchesInterne,
            pourcentageGN: tauxGazTorchesInterne,
            sourceType: 'interne',
            category: 'gazTorches',
          });
        }

        if (gazTorchesExterne > 0) {
          data.push({
            label: 'Gaz Torchés externe',
            cm3GN: gazTorchesExterne,
            pourcentageGN: tauxGazTorchesExterne,
            sourceType: 'externe',
            category: 'gazTorches',
          });
        }

        return {
          mois: moisNom,
          annee: annee,
          data: data
        };
      }),
      catchError(error => {
        console.error('Erreur lors de la récupération des données d\'autoconsommation:', error);
        // Retourner des données vides en cas d'erreur
        return of({
          mois: 'N/A',
          annee: 'N/A',
          data: []
        });
      })
      );
    });
  }

  /**
   * Version simplifiée qui utilise des valeurs par défaut pour la compatibilité
   * @returns Observable contenant les données d'autoconsommation avec des paramètres par défaut
   */
  getAutoconsommationDataDefault(): Observable<AutoconsommationRecap> {
    // Utiliser des valeurs par défaut - vous pouvez les adapter selon vos besoins
    const uniteParDefaut = '5X3'; // Unité par défaut
    const maintenant = new Date();
    const pmoisParDefaut = `01${(maintenant.getMonth() + 1).toString().padStart(2, '0')}${maintenant.getFullYear()}`;

    return this.getAutoconsommationData(uniteParDefaut, pmoisParDefaut);
  }

  /**
   * Récupère les données de consommation énergétique depuis la vue CONSOMMATION_ENERGETIQUE
   * @param unite Code de l'unité (ex: "5X2")
   * @param pmois Période au format ddMMyyyy (ex: "01122024")
   * @returns Observable contenant les données de consommation énergétique
   */
  getConsommationEnergetique(unite: string, pmois: string): Observable<AutoconsommationRecap> {
    const cacheKey = this.cacheService.generateKey('consommation-energetique', unite, pmois);

    return this.cacheService.getOrSet(cacheKey, () => {
      const url = `${this.baseUrl}/consommation-energetique/${pmois}/${unite}`;

      return this.http.get<any>(url).pipe(
        map((consommationData) => {
          // Extraire mois et année de pmois (format ddMMyyyy)
          const date = new Date(
            parseInt(pmois.substring(4, 8)), // année
            parseInt(pmois.substring(2, 4)) - 1, // mois (0-indexé)
            parseInt(pmois.substring(0, 2)) // jour
          );
          const moisNom = date.toLocaleDateString('fr-FR', { month: 'long' });
          const annee = date.getFullYear().toString();

          const data: AutoconsommationData[] = [];

          // Ajouter CE Sonelgaz si présent
          if (consommationData.ceElectriciteSonalgaz && consommationData.ceElectriciteSonalgaz > 0) {
            data.push({
              label: 'CE Sonelgaz',
              cm3GN: consommationData.ceElectriciteSonalgaz,
              pourcentageGN: 0, // Le pourcentage sera calculé côté frontend si nécessaire
              sourceType: 'externe',
              category: 'CE',
            });
          }

          // Ajouter CE Kahrama si présent
          if (consommationData.ceKahrama && consommationData.ceKahrama > 0) {
            data.push({
              label: 'CE Kahrama',
              cm3GN: consommationData.ceKahrama,
              pourcentageGN: 0, // Le pourcentage sera calculé côté frontend si nécessaire
              sourceType: 'externe',
              category: 'CE',
            });
          }

          return {
            mois: moisNom,
            annee: annee,
            data: data
          };
        }),
        catchError(error => {
          console.error('Erreur lors de la récupération des données de consommation énergétique:', error);
          // Retourner des données vides en cas d'erreur
          return of({
            mois: 'N/A',
            annee: 'N/A',
            data: []
          });
        })
      );
    });
  }

  getConsommationDetail(): Observable<DetailMensuel> {
    // Données mockées en attendant l'implémentation du backend
    const mockData: DetailMensuel = {
      mois: 'Novembre',
      annee: '2024',
      postes: [
        {
          poste: 'Poste 1',
          cm3GN: 16615666,
          pourcentageGN: 2.68,
        },
        {
          poste: 'Poste 2',
          cm3GN: 2465746,
          pourcentageGN: 0.4,
        },
        {
          poste: 'Poste 3',
          cm3GN: 3654654,
          pourcentageGN: 0.59,
        },
      ],
      donneesJournalieres: Array.from({ length: 30 }, (_, i) => {
        const jour = i + 1;
        const analyses = {
          5: {
            'Poste 1':
              'Pic de consommation dû à une augmentation de la production',
            'Poste 2': 'Maintenance préventive effectuée',
          },
          12: {
            'Poste 3': 'Arrêt technique planifié',
          },
          15: {
            'Poste 1': 'Optimisation du processus mise en place',
            'Poste 2': 'Test de nouveaux paramètres',
          },
          25: {
            'Poste 1': 'Incident technique résolu',
            'Poste 3': 'Performance optimale atteinte',
          },
        };

        return ['Poste 1', 'Poste 2', 'Poste 3'].map((poste) => ({
          jour,
          poste,
          cm3GN: Math.floor(
            (poste === 'Poste 1'
              ? 500000
              : poste === 'Poste 2'
              ? 70000
              : 100000) +
              Math.random() *
                (poste === 'Poste 1'
                  ? 100000
                  : poste === 'Poste 2'
                  ? 30000
                  : 50000)
          ),
          analyse:
            analyses[jour as keyof typeof analyses]?.[
              poste as keyof (typeof analyses)[keyof typeof analyses]
            ],
        }));
      }).flat(),
    };
    return of(mockData);
  }

  getGazTorchesDetail(): Observable<DetailMensuel> {
    // Données mockées en attendant l'implémentation du backend
    const mockData: DetailMensuel = {
      mois: 'Novembre',
      annee: '2024',
      causes: [
        {
          cause: 'Cause 1',
          cm3GN: 2500000,
          pourcentageGN: 0.4,
        },
        {
          cause: 'Cause 2',
          cm3GN: 1500000,
          pourcentageGN: 0.24,
        },
        {
          cause: 'Cause 3',
          cm3GN: 1000000,
          pourcentageGN: 0.16,
        },
        {
          cause: 'Cause 4',
          cm3GN: 989109,
          pourcentageGN: 0.16,
        },
        {
          cause: 'Autres',
          cm3GN: 500000,
          pourcentageGN: 0.09,
        },
      ],
      donneesJournalieres: Array.from({ length: 30 }, (_, i) => {
        const jour = i + 1;
        const analyses = {
          3: {
            'Cause 1':
              "Dépassement du seuil critique - Intervention d'urgence requise",
            'Cause 2': 'Maintenance corrective effectuée',
          },
          8: {
            'Cause 3': 'Problème de régulation identifié',
            'Cause 4': 'Mise en place de mesures correctives',
          },
          17: {
            'Cause 1': 'Pic anormal - Investigation en cours',
            Autres: 'Causes multiples identifiées',
          },
          22: {
            'Cause 2': 'Défaillance technique résolue',
            'Cause 3': 'Optimisation du processus effectuée',
          },
        };

        return ['Cause 1', 'Cause 2', 'Cause 3', 'Cause 4', 'Autres'].map(
          (cause) => ({
            jour,
            cause,
            cm3GN: Math.floor(
              (cause === 'Cause 1'
                ? 80000
                : cause === 'Cause 2'
                ? 45000
                : cause === 'Cause 3'
                ? 30000
                : cause === 'Cause 4'
                ? 30000
                : 15000) +
                Math.random() *
                  (cause === 'Cause 1'
                    ? 20000
                    : cause === 'Cause 2'
                    ? 15000
                    : cause === 'Cause 3'
                    ? 10000
                    : cause === 'Cause 4'
                    ? 10000
                    : 5000)
            ),
            analyse:
              analyses[jour as keyof typeof analyses]?.[
                cause as keyof (typeof analyses)[keyof typeof analyses]
              ],
          })
        );
      }).flat(),
    };
    return of(mockData);
  }

  /**
   * Récupère le bilan global pour un complexe et une période donnés
   * @param complexId identifiant du complexe (ex. "GL2Z")
   * @param period période au format YYYY-MM (ex. "2024-12")
   */
  getBilanGlobal(complexId: string, period: string): Observable<BilanGlobalComplexe> {
    return this.http.get<BilanGlobalComplexe[]>(this.localUrl).pipe(
      map(list => {
        const found = list.find(item =>
          item.complexId === complexId && item.period === period
        );
        if (!found) {
          throw new Error(`Bilan introuvable pour ${complexId} / ${period}`);
        }
        return found;
      }),
      catchError(err => throwError(() => err))
    );
  }

  /**
   * Récupère les indicateurs de performance pour une unité et une période donnés
   * @param pmois Période au format ddMMyyyy (ex: "01122024")
   * @param unite Code de l'unité (ex: "5X2")
   * @returns Observable contenant les indicateurs de performance
   */
  getIndicateurPerformance(pmois: string, unite: string): Observable<IndicateurPerformanceUnite[]> {
    const url = `${this.baseUrl}/indicateur-performance/${pmois}/${unite}`;
    return this.http.get<IndicateurPerformanceUnite[]>(url);
  }

  /**
   * Récupère les données de gaz torchés par cause de torchage
   * @param unite Code de l'unité (ex: "5X2")
   * @param pmois Période au format ddMMyyyy (ex: "01122024")
   * @returns Observable contenant les données de gaz torchés
   */
  getGazTorcheeParCauseTorchage(unite: string, pmois: string): Observable<GazTorcheeParCauseTorchage[]> {
    const url = `${this.baseUrl}/gaz-torchee-par-cause-torchage/${unite}/${pmois}`;
    return this.http.get<GazTorcheeParCauseTorchage[]>(url).pipe(
      catchError(() => of([])) // Si pas de données, retourner tableau vide
    );
  }

  /**
   * Récupère les données d'utilisation des chaudières
   * @param unite Code de l'unité (ex: "5X2")
   * @param pmois Période au format ddMMyyyy (ex: "01122024")
   * @returns Observable contenant les données d'utilisation des chaudières
   */
  getUtilisationChaudieres(unite: string, pmois: string): Observable<UtilisationChaudieres[]> {
    const url = `${this.baseUrl}/utilisation-chaudieres/${unite}/${pmois}`;
    return this.http.get<UtilisationChaudieres[]>(url).pipe(
      catchError(() => of([])) // Si pas de données, retourner tableau vide
    );
  }

  /**
   * Récupère les détails de gaz torché depuis le contrôleur GazTorchDetailsController
   * @param pmois Période au format ddMMyyyy (ex. "01122024")
   * @param unite Code de l'unité (ex. "5X3")
   * @returns Observable contenant la liste des détails de gaz torché
   */
  getGazTorchDetails(pmois: string, unite: string): Observable<GazTorchDetails[]> {
    const url = `${this.baseUrl}/gaz-torche/${pmois}/${unite}`;
    return this.http.get<GazTorchDetails[]>(url).pipe(
      catchError(err => {
        console.error('Erreur lors de la récupération des détails de gaz torché:', err);
        return of([]); // Retourner un tableau vide en cas d'erreur
      })
    );
  }

  /**
   * Récupère la liste des unités avec leurs objectifs et taux design d'autoconsommation
   * depuis la vue LIST_UNITE (avec cache)
   * @returns Observable contenant la liste des unités avec leurs taux objectif et design
   */
  getListUnites(): Observable<ListUnite[]> {
    const cacheKey = this.cacheService.generateKey('list-unites');

    return this.cacheService.getOrSet(cacheKey, () => {
      const url = `${this.baseUrl}/list-unites`;
      return this.http.get<ListUnite[]>(url).pipe(
        catchError(err => {
          console.error('Erreur lors de la récupération de la liste des unités:', err);
          return of([]); // Retourner un tableau vide en cas d'erreur
        })
      );
    }, 10 * 60 * 1000); // Cache de 10 minutes pour la liste des unités
  }

  /**
   * Récupère les taux objectif et design d'autoconsommation pour une unité et période spécifiques
   * @param unite Code de l'unité (ex: "5X2")
   * @param pmois Période au format ddMMyyyy (ex: "01122024")
   * @returns Observable contenant les données de l'unité ou null si non trouvée
   */
  getUniteObjectifs(unite: string, pmois: string): Observable<ListUnite | null> {
    // Utiliser l'endpoint spécifique pour récupérer directement l'unité
    const url = `${this.baseUrl}/list-unites/${unite}/${pmois}`;
    return this.http.get<ListUnite>(url).pipe(
      catchError(err => {
        console.error('Erreur lors de la récupération des objectifs de l\'unité:', err);
        return of(null);
      })
    );
  }
}
