,--.   ,--.                                      /  /  ,-----.               ,--.    ,------.           ,--. 
|   `.'   | ,--,--.,--. ,--.,--,--. ,--,--.     /  /   |  |) /_  ,--,--.,---.|  |,-. |  .---',--,--,  ,-|  | 
|  |'.'|  |' ,-.  | \  '  /' ,-.  |' ,-.  |    /  /    |  .-.  \' ,-.  | .--'|     / |  `--, |      \' .-. | 
|  |   |  |\ '-'  |  \   ' \ '-'  |\ '-'  |   /  /     |  '--' /\ '-'  \ `--.|  \  \ |  `---.|  ||  |\ `-' | 
`--'   `--' `--`--'.-'  /   `--`--' `--`--'  /  /      `------'  `--`--'`---'`--'`--'`------'`--''--' `---'  
                   `---'                    `--'                                                             
${application.title} ${application.version}
Powered by Spring Boot ${spring-boot.version}