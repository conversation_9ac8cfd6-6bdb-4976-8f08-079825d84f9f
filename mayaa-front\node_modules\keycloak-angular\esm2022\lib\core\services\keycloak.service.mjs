import { Injectable } from '@angular/core';
import { HttpHeaders } from '@angular/common/http';
import { Subject, from } from 'rxjs';
import { map } from 'rxjs/operators';
import Keycloak from 'keycloak-js';
import { KeycloakEventType } from '../interfaces/keycloak-event';
import * as i0 from "@angular/core";
export class KeycloakService {
    constructor() {
        this._keycloakEvents$ = new Subject();
    }
    bindsKeycloakEvents() {
        this._instance.onAuthError = (errorData) => {
            this._keycloakEvents$.next({
                args: errorData,
                type: KeycloakEventType.OnAuthError
            });
        };
        this._instance.onAuthLogout = () => {
            this._keycloakEvents$.next({ type: KeycloakEventType.OnAuthLogout });
        };
        this._instance.onAuthRefreshSuccess = () => {
            this._keycloakEvents$.next({
                type: KeycloakEventType.OnAuthRefreshSuccess
            });
        };
        this._instance.onAuthRefreshError = () => {
            this._keycloakEvents$.next({
                type: KeycloakEventType.OnAuthRefreshError
            });
        };
        this._instance.onAuthSuccess = () => {
            this._keycloakEvents$.next({ type: KeycloakEventType.OnAuthSuccess });
        };
        this._instance.onTokenExpired = () => {
            this._keycloakEvents$.next({
                type: KeycloakEventType.OnTokenExpired
            });
        };
        this._instance.onActionUpdate = (state) => {
            this._keycloakEvents$.next({
                args: state,
                type: KeycloakEventType.OnActionUpdate
            });
        };
        this._instance.onReady = (authenticated) => {
            this._keycloakEvents$.next({
                args: authenticated,
                type: KeycloakEventType.OnReady
            });
        };
    }
    loadExcludedUrls(bearerExcludedUrls) {
        const excludedUrls = [];
        for (const item of bearerExcludedUrls) {
            let excludedUrl;
            if (typeof item === 'string') {
                excludedUrl = { urlPattern: new RegExp(item, 'i'), httpMethods: [] };
            }
            else {
                excludedUrl = {
                    urlPattern: new RegExp(item.url, 'i'),
                    httpMethods: item.httpMethods
                };
            }
            excludedUrls.push(excludedUrl);
        }
        return excludedUrls;
    }
    initServiceValues({ enableBearerInterceptor = true, loadUserProfileAtStartUp = false, bearerExcludedUrls = [], authorizationHeaderName = 'Authorization', bearerPrefix = 'Bearer', initOptions, updateMinValidity = 20, shouldAddToken = () => true, shouldUpdateToken = () => true }) {
        this._enableBearerInterceptor = enableBearerInterceptor;
        this._loadUserProfileAtStartUp = loadUserProfileAtStartUp;
        this._authorizationHeaderName = authorizationHeaderName;
        this._bearerPrefix = bearerPrefix.trim().concat(' ');
        this._excludedUrls = this.loadExcludedUrls(bearerExcludedUrls);
        this._silentRefresh = initOptions ? initOptions.flow === 'implicit' : false;
        this._updateMinValidity = updateMinValidity;
        this.shouldAddToken = shouldAddToken;
        this.shouldUpdateToken = shouldUpdateToken;
    }
    async init(options = {}) {
        this.initServiceValues(options);
        const { config, initOptions } = options;
        this._instance = new Keycloak(config);
        this.bindsKeycloakEvents();
        const authenticated = await this._instance.init(initOptions);
        if (authenticated && this._loadUserProfileAtStartUp) {
            await this.loadUserProfile();
        }
        return authenticated;
    }
    async login(options = {}) {
        await this._instance.login(options);
        if (this._loadUserProfileAtStartUp) {
            await this.loadUserProfile();
        }
    }
    async logout(redirectUri) {
        const options = {
            redirectUri
        };
        await this._instance.logout(options);
        this._userProfile = undefined;
    }
    async register(options = { action: 'register' }) {
        await this._instance.register(options);
    }
    isUserInRole(role, resource) {
        let hasRole;
        hasRole = this._instance.hasResourceRole(role, resource);
        if (!hasRole) {
            hasRole = this._instance.hasRealmRole(role);
        }
        return hasRole;
    }
    getUserRoles(realmRoles = true, resource) {
        let roles = [];
        if (this._instance.resourceAccess) {
            Object.keys(this._instance.resourceAccess).forEach((key) => {
                if (resource && resource !== key) {
                    return;
                }
                const resourceAccess = this._instance.resourceAccess[key];
                const clientRoles = resourceAccess['roles'] || [];
                roles = roles.concat(clientRoles);
            });
        }
        if (realmRoles && this._instance.realmAccess) {
            const realmRoles = this._instance.realmAccess['roles'] || [];
            roles.push(...realmRoles);
        }
        return roles;
    }
    isLoggedIn() {
        if (!this._instance) {
            return false;
        }
        return this._instance.authenticated;
    }
    isTokenExpired(minValidity = 0) {
        return this._instance.isTokenExpired(minValidity);
    }
    async updateToken(minValidity = this._updateMinValidity) {
        if (this._silentRefresh) {
            if (this.isTokenExpired()) {
                throw new Error('Failed to refresh the token, or the session is expired');
            }
            return true;
        }
        if (!this._instance) {
            throw new Error('Keycloak Angular library is not initialized.');
        }
        try {
            return await this._instance.updateToken(minValidity);
        }
        catch (error) {
            return false;
        }
    }
    async loadUserProfile(forceReload = false) {
        if (this._userProfile && !forceReload) {
            return this._userProfile;
        }
        if (!this._instance.authenticated) {
            throw new Error('The user profile was not loaded as the user is not logged in.');
        }
        return (this._userProfile = await this._instance.loadUserProfile());
    }
    async getToken() {
        return this._instance.token;
    }
    getUsername() {
        if (!this._userProfile) {
            throw new Error('User not logged in or user profile was not loaded.');
        }
        return this._userProfile.username;
    }
    clearToken() {
        this._instance.clearToken();
    }
    addTokenToHeader(headers = new HttpHeaders()) {
        return from(this.getToken()).pipe(map((token) => token
            ? headers.set(this._authorizationHeaderName, this._bearerPrefix + token)
            : headers));
    }
    getKeycloakInstance() {
        return this._instance;
    }
    get excludedUrls() {
        return this._excludedUrls;
    }
    get enableBearerInterceptor() {
        return this._enableBearerInterceptor;
    }
    get keycloakEvents$() {
        return this._keycloakEvents$;
    }
    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: "12.0.0", version: "17.0.3", ngImport: i0, type: KeycloakService, deps: [], target: i0.ɵɵFactoryTarget.Injectable }); }
    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: "12.0.0", version: "17.0.3", ngImport: i0, type: KeycloakService }); }
}
i0.ɵɵngDeclareClassMetadata({ minVersion: "12.0.0", version: "17.0.3", ngImport: i0, type: KeycloakService, decorators: [{
            type: Injectable
        }] });
//# sourceMappingURL=data:application/json;base64,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