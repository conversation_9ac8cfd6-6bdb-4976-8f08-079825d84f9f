/**
 * Interface représentant les données de la table M_UNITE
 * Contient les informations sur les unités avec leurs groupes Keycloak associés
 */
export interface MUnite {
  /** Identifiant unique de l'unité */
  idUnite: number;

  /** Description de l'unité (ex: "GL1Z", "GL2Z") */
  descUnite: string;

  /** Code de l'unité (ex: "5X2", "5X3") */
  codeUnite: string;

  /** ID du groupe Keycloak associé à cette unité */
  keycloakGroupId: string;
}
