package dz.sonatrach.weblqs.mayaaback.controller;

import com.fasterxml.jackson.annotation.JsonView;
import dz.sonatrach.weblqs.mayaaback.model.MUnite;
import dz.sonatrach.weblqs.mayaaback.repo.MUniteRepository;
import dz.sonatrach.weblqs.mayaaback.views.View;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * Contrôleur pour l'accès aux données de la table M_UNITE avec filtrage par groupes Keycloak.
 * 
 * Endpoints principaux :
 *   GET /api/m-unites - Récupère toutes les unités avec leurs groupes Keycloak
 *   POST /api/m-unites/by-groups - Récupère les unités filtrées par groupes Keycloak
 */
@RestController
@RequestMapping("api/")
public class MUniteController {

    @Autowired
    private MUniteRepository mUniteRepository;

    /**
     * Récupère toutes les unités avec leurs groupes Keycloak
     * @return Liste de toutes les unités
     */
    @GetMapping("/m-unites")
    @JsonView(View.basic.class)
    public ResponseEntity<List<MUnite>> getAllUnites() {
        List<MUnite> unites = mUniteRepository.findAll();
        if (unites.isEmpty()) {
            return ResponseEntity.noContent().build();
        }
        return ResponseEntity.ok(unites);
    }

    /**
     * Récupère les unités accessibles pour les groupes Keycloak donnés
     * @param groupIds Liste des IDs de groupes Keycloak de l'utilisateur
     * @return Liste des unités accessibles (exclut les entrées avec keycloakGroupId = "EXP")
     */
    @PostMapping("/m-unites/by-groups")
    @JsonView(View.basic.class)
    public ResponseEntity<List<MUnite>> getUnitesByGroups(@RequestBody List<String> groupIds) {
        if (groupIds == null || groupIds.isEmpty()) {
            return ResponseEntity.badRequest().build();
        }

        List<MUnite> unites;

        // Si l'utilisateur a le groupe "EXP", il a accès à toutes les unités
        if (groupIds.contains("EXP")) {
            unites = mUniteRepository.findAll();
        } else {
            // Sinon, filtrer par les groupes de l'utilisateur
            unites = mUniteRepository.findByKeycloakGroupIdIn(groupIds);
        }

        // Filtrer pour exclure les entrées avec keycloakGroupId = "EXP"
        // (ce ne sont pas de vraies unités mais des marqueurs de permission)
        unites = unites.stream()
                .filter(unite -> !"EXP".equals(unite.getKeycloakGroupId()))
                .filter(unite -> unite.getCodeUnite() != null && !unite.getCodeUnite().trim().isEmpty())
                .filter(unite -> unite.getDescUnite() != null && !unite.getDescUnite().trim().isEmpty())
                .collect(java.util.stream.Collectors.toList());

        if (unites.isEmpty()) {
            return ResponseEntity.noContent().build();
        }
        return ResponseEntity.ok(unites);
    }
}
