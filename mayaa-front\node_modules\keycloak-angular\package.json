{"name": "keycloak-angular", "version": "15.3.0", "description": "Easy Keycloak setup for Angular applications", "repository": {"type": "git", "url": "git+ssh://**************/mauriciovigolo/keycloak-angular.git"}, "author": {"name": "<PERSON><PERSON><PERSON>", "email": "@mauriciovigolo", "url": "https://github.com/mauriciovigolo"}, "license": "MIT", "bugs": {"url": "https://github.com/mauriciovigolo/keycloak-angular/issues"}, "homepage": "https://github.com/mauriciovigolo/keycloak-angular#readme", "keywords": ["angular", "keycloak", "keycloak-js", "authentication", "authorization", "oauth2", "oidc"], "peerDependencies": {"@angular/common": "^17", "@angular/core": "^17", "@angular/router": "^17", "keycloak-js": "^18 || ^19 || ^20 || ^21 || ^22 || ^23 || ^24 || ^25"}, "dependencies": {"tslib": "^2.3.1"}, "module": "fesm2022/keycloak-angular.mjs", "typings": "index.d.ts", "exports": {"./package.json": {"default": "./package.json"}, ".": {"types": "./index.d.ts", "esm2022": "./esm2022/keycloak-angular.mjs", "esm": "./esm2022/keycloak-angular.mjs", "default": "./fesm2022/keycloak-angular.mjs"}}, "sideEffects": false}