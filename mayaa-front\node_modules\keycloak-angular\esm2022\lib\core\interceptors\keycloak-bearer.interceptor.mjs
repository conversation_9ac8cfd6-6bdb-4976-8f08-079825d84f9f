import { Injectable } from '@angular/core';
import { combineLatest, from, of } from 'rxjs';
import { mergeMap } from 'rxjs/operators';
import * as i0 from "@angular/core";
import * as i1 from "../services/keycloak.service";
export class KeycloakBearerInterceptor {
    constructor(keycloak) {
        this.keycloak = keycloak;
    }
    async conditionallyUpdateToken(req) {
        if (this.keycloak.shouldUpdateToken(req)) {
            return await this.keycloak.updateToken();
        }
        return true;
    }
    isUrlExcluded({ method, url }, { urlPattern, httpMethods }) {
        const httpTest = httpMethods.length === 0 ||
            httpMethods.join().indexOf(method.toUpperCase()) > -1;
        const urlTest = urlPattern.test(url);
        return httpTest && urlTest;
    }
    intercept(req, next) {
        const { enableBearerInterceptor, excludedUrls } = this.keycloak;
        if (!enableBearerInterceptor) {
            return next.handle(req);
        }
        const shallPass = !this.keycloak.shouldAddToken(req) ||
            excludedUrls.findIndex((item) => this.isUrlExcluded(req, item)) > -1;
        if (shallPass) {
            return next.handle(req);
        }
        return combineLatest([
            from(this.conditionallyUpdateToken(req)),
            of(this.keycloak.isLoggedIn())
        ]).pipe(mergeMap(([_, isLoggedIn]) => isLoggedIn
            ? this.handleRequestWithTokenHeader(req, next)
            : next.handle(req)));
    }
    handleRequestWithTokenHeader(req, next) {
        return this.keycloak.addTokenToHeader(req.headers).pipe(mergeMap((headersWithBearer) => {
            const kcReq = req.clone({ headers: headersWithBearer });
            return next.handle(kcReq);
        }));
    }
    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: "12.0.0", version: "17.0.3", ngImport: i0, type: KeycloakBearerInterceptor, deps: [{ token: i1.KeycloakService }], target: i0.ɵɵFactoryTarget.Injectable }); }
    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: "12.0.0", version: "17.0.3", ngImport: i0, type: KeycloakBearerInterceptor }); }
}
i0.ɵɵngDeclareClassMetadata({ minVersion: "12.0.0", version: "17.0.3", ngImport: i0, type: KeycloakBearerInterceptor, decorators: [{
            type: Injectable
        }], ctorParameters: () => [{ type: i1.KeycloakService }] });
//# sourceMappingURL=data:application/json;base64,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