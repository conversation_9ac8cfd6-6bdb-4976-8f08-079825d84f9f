{"version": 3, "file": "keycloak-authz.min.js", "sources": ["../../../node_modules/.pnpm/es6-promise@4.2.8/node_modules/es6-promise/dist/es6-promise.min.js", "../src/keycloak-authz.js"], "sourcesContent": null, "names": ["module", "exports", "t", "e", "n", "W", "r", "z", "o", "process", "nextTick", "a", "i", "U", "c", "s", "H", "document", "createTextNode", "observe", "characterData", "data", "u", "MessageChannel", "port1", "onmessage", "port2", "postMessage", "setTimeout", "N", "Q", "f", "Function", "require", "runOnLoop", "runOnContext", "l", "this", "constructor", "v", "V", "x", "_state", "arguments", "T", "_result", "j", "h", "w", "p", "TypeError", "d", "_", "call", "y", "A", "S", "_label", "m", "Z", "$", "b", "resolve", "then", "g", "_onerror", "E", "X", "_subscribers", "length", "M", "P", "tt", "C", "Error", "O", "et", "promise", "k", "L", "F", "Y", "q", "D", "global", "self", "Promise", "Object", "prototype", "toString", "cast", "nt", "K", "Array", "isArray", "R", "B", "window", "G", "MutationObserver", "WebKitMutationObserver", "I", "J", "Uint8ClampedArray", "importScripts", "Math", "random", "substring", "_instanceConstructor", "_remaining", "_enumerate", "_eachEntry", "_settledAt", "_willSettleAt", "all", "race", "reject", "_setScheduler", "_setAsap", "_asap", "polyfill", "keycloak", "options", "_instance", "rpt", "indexOf", "ready", "res", "rej", "init", "request", "XMLHttpRequest", "open", "authServerUrl", "realm", "onreadystatechange", "readyState", "status", "config", "JSON", "parse", "responseText", "console", "error", "send", "authorize", "authorizationRequest", "onGrant", "onDeny", "onError", "ticket", "token_endpoint", "setRequestHeader", "token", "access_token", "params", "clientId", "undefined", "submitRequest", "metadata", "responseIncludeResourceName", "responsePermissionsLimit", "incrementalAuthorization", "entitlement", "resourceServerId", "claimToken", "claimTokenFormat", "permissions", "resource", "permission", "id", "scopes", "scope"], "mappings": "scAAoEA,EAAAC,QAA0F,WAAwB,SAASC,EAAEA,GAAG,IAAIC,SAASD,EAAE,OAAO,OAAOA,IAAI,WAAWC,GAAG,aAAaA,EAAE,CAAC,SAASA,EAAED,GAAG,MAAM,mBAAmBA,CAAC,CAAC,SAASE,EAAEF,GAAGG,EAAEH,CAAC,CAAC,SAASI,EAAEJ,GAAGK,EAAEL,CAAC,CAAC,SAASM,IAAI,OAAO,WAAW,OAAOC,QAAQC,SAASC,EAAE,CAAC,CAAC,SAASC,IAAI,YAAM,IAAoBC,EAAE,WAAWA,EAAEF,EAAE,EAAEG,GAAG,CAAC,SAASC,IAAI,IAAIb,EAAE,EAAEC,EAAE,IAAIa,EAAEL,GAAGP,EAAEa,SAASC,eAAe,IAAI,OAAOf,EAAEgB,QAAQf,EAAE,CAACgB,eAAc,IAAK,WAAWhB,EAAEiB,KAAKnB,IAAIA,EAAE,CAAC,CAAC,CAAC,SAASoB,IAAI,IAAIpB,EAAE,IAAIqB,eAAe,OAAOrB,EAAEsB,MAAMC,UAAUd,EAAE,WAAW,OAAOT,EAAEwB,MAAMC,YAAY,EAAE,CAAC,CAAC,SAASb,IAAI,IAAIZ,EAAE0B,WAAW,OAAO,WAAW,OAAO1B,EAAES,EAAE,EAAE,CAAC,CAAC,SAASA,IAAI,IAAI,IAAIT,EAAE,EAAEA,EAAE2B,EAAE3B,GAAG,GAAuBC,EAAd2B,EAAE5B,IAAK4B,EAAE5B,EAAE,IAAQ4B,EAAE5B,QAAG,EAAO4B,EAAE5B,EAAE,QAAG,EAAO2B,EAAE,CAAC,CAAC,SAASE,IAAI,IAAI,IAAI7B,EAAE8B,SAAS,cAATA,GAA0BC,QAAQ,SAAS,OAAOpB,EAAEX,EAAEgC,WAAWhC,EAAEiC,aAAavB,GAAG,CAAC,MAAMT,GAAG,OAAOW,GAAG,CAAC,CAAC,SAASsB,EAAElC,EAAEC,GAAG,IAAIC,EAAEiC,KAAK/B,EAAE,IAAI+B,KAAKC,YAAYC,QAAG,IAASjC,EAAEkC,IAAIC,EAAEnC,GAAG,IAAIE,EAAEJ,EAAEsC,OAAO,GAAGlC,EAAE,CAAC,IAAII,EAAE+B,UAAUnC,EAAE,GAAGD,GAAE,WAAW,OAAOqC,EAAEpC,EAAEF,EAAEM,EAAER,EAAEyC,QAAQ,GAAE,MAAMC,EAAE1C,EAAEE,EAAEJ,EAAEC,GAAG,OAAOG,CAAC,CAAC,SAASyC,EAAE7C,GAAG,IAAIC,EAAEkC,KAAK,GAAGnC,GAAG,iBAAiBA,GAAGA,EAAEoC,cAAcnC,EAAE,OAAOD,EAAE,IAAIE,EAAE,IAAID,EAAEoC,GAAG,OAAOS,EAAE5C,EAAEF,GAAGE,CAAC,CAAC,SAASmC,IAAG,CAAE,SAASU,IAAI,OAAO,IAAIC,UAAU,2CAA2C,CAAC,SAASC,IAAI,OAAO,IAAID,UAAU,uDAAuD,CAAC,SAASE,EAAElD,EAAEC,EAAEC,EAAEE,GAAG,IAAIJ,EAAEmD,KAAKlD,EAAEC,EAAEE,EAAE,CAAC,MAAME,GAAG,OAAOA,CAAC,CAAC,CAAC,SAAS8C,EAAEpD,EAAEC,EAAEC,GAAGG,GAAE,SAASL,GAAG,IAAII,GAAE,EAAGE,EAAE4C,EAAEhD,EAAED,GAAE,SAASC,GAAGE,IAAIA,GAAE,EAAGH,IAAIC,EAAE4C,EAAE9C,EAAEE,GAAGmD,EAAErD,EAAEE,GAAG,IAAE,SAASD,GAAGG,IAAIA,GAAE,EAAGkD,EAAEtD,EAAEC,GAAG,GAAE,YAAYD,EAAEuD,QAAQ,sBAAsBnD,GAAGE,IAAIF,GAAE,EAAGkD,EAAEtD,EAAEM,GAAG,GAAEN,EAAE,CAAC,SAASwD,EAAExD,EAAEC,GAAGA,EAAEuC,SAASiB,EAAEJ,EAAErD,EAAEC,EAAE0C,SAAS1C,EAAEuC,SAASkB,GAAEJ,EAAEtD,EAAEC,EAAE0C,SAASC,EAAE3C,OAAE,GAAO,SAASA,GAAG,OAAO6C,EAAE9C,EAAEC,EAAE,IAAE,SAASA,GAAG,OAAOqD,EAAEtD,EAAEC,EAAE,GAAE,CAAC,SAAS0D,EAAE3D,EAAEE,EAAEE,GAAGF,EAAEkC,cAAcpC,EAAEoC,aAAahC,IAAI8B,GAAGhC,EAAEkC,YAAYwB,UAAUf,EAAEW,EAAExD,EAAEE,QAAG,IAASE,EAAEiD,EAAErD,EAAEE,GAAGD,EAAEG,GAAGgD,EAAEpD,EAAEE,EAAEE,GAAGiD,EAAErD,EAAEE,EAAE,CAAC,SAAS4C,EAAE7C,EAAEC,GAAG,GAAGD,IAAIC,EAAEoD,EAAErD,EAAE8C,UAAU,GAAG/C,EAAEE,GAAG,CAAC,IAAIE,OAAE,EAAO,IAAIA,EAAEF,EAAE2D,IAAI,CAAC,MAAMvD,GAAG,YAAYgD,EAAErD,EAAEK,EAAE,CAACqD,EAAE1D,EAAEC,EAAEE,EAAE,MAAMiD,EAAEpD,EAAEC,EAAE,CAAC,SAAS4D,EAAE9D,GAAGA,EAAE+D,UAAU/D,EAAE+D,SAAS/D,EAAE2C,SAASqB,EAAEhE,EAAE,CAAC,SAASqD,EAAErD,EAAEC,GAAGD,EAAEwC,SAASyB,IAAIjE,EAAE2C,QAAQ1C,EAAED,EAAEwC,OAAOiB,EAAE,IAAIzD,EAAEkE,aAAaC,QAAQ9D,EAAE2D,EAAEhE,GAAG,CAAC,SAASsD,EAAEtD,EAAEC,GAAGD,EAAEwC,SAASyB,IAAIjE,EAAEwC,OAAOkB,GAAE1D,EAAE2C,QAAQ1C,EAAEI,EAAEyD,EAAE9D,GAAG,CAAC,SAAS4C,EAAE5C,EAAEC,EAAEC,EAAEE,GAAG,IAAIE,EAAEN,EAAEkE,aAAaxD,EAAEJ,EAAE6D,OAAOnE,EAAE+D,SAAS,KAAKzD,EAAEI,GAAGT,EAAEK,EAAEI,EAAE+C,GAAGvD,EAAEI,EAAEI,EAAEgD,IAAGtD,EAAE,IAAIM,GAAGV,EAAEwC,QAAQnC,EAAE2D,EAAEhE,EAAE,CAAC,SAASgE,EAAEhE,GAAG,IAAIC,EAAED,EAAEkE,aAAahE,EAAEF,EAAEwC,OAAO,GAAG,IAAIvC,EAAEkE,OAAO,CAAC,IAAI,IAAI/D,OAAE,EAAOE,OAAE,EAAOI,EAAEV,EAAE2C,QAAQ9B,EAAE,EAAEA,EAAEZ,EAAEkE,OAAOtD,GAAG,EAAET,EAAEH,EAAEY,GAAGP,EAAEL,EAAEY,EAAEX,GAAGE,EAAEsC,EAAExC,EAAEE,EAAEE,EAAEI,GAAGJ,EAAEI,GAAGV,EAAEkE,aAAaC,OAAO,CAAC,CAAC,CAAC,SAASzB,EAAE1C,EAAEE,EAAEE,EAAEE,GAAG,IAAII,EAAET,EAAEG,GAAGS,OAAE,EAAOO,OAAE,EAAOR,GAAE,EAAG,GAAGF,EAAE,CAAC,IAAIG,EAAET,EAAEE,EAAE,CAAC,MAAMG,GAAGG,GAAE,EAAGQ,EAAEX,CAAC,CAAC,GAAGP,IAAIW,EAAE,YAAYyC,EAAEpD,EAAE+C,IAAI,MAAMpC,EAAEP,EAAEJ,EAAEsC,SAASyB,IAAIvD,GAAGE,EAAEkC,EAAE5C,EAAEW,IAAO,IAAJD,EAAO0C,EAAEpD,EAAEkB,GAAGpB,IAAIyD,EAAEJ,EAAEnD,EAAEW,GAAGb,IAAI0D,IAAGJ,EAAEpD,EAAEW,GAAG,CAAC,SAASuD,EAAEpE,EAAEC,GAAG,IAAIA,GAAE,SAASA,GAAG6C,EAAE9C,EAAEC,EAAE,IAAE,SAASA,GAAGqD,EAAEtD,EAAEC,EAAE,GAAE,CAAC,MAAMC,GAAGoD,EAAEtD,EAAEE,EAAE,CAAC,CAAC,SAASmE,IAAI,OAAOC,IAAI,CAAC,SAAS/B,EAAEvC,GAAGA,EAAEsC,GAAGgC,KAAKtE,EAAEwC,YAAO,EAAOxC,EAAE2C,aAAQ,EAAO3C,EAAEkE,aAAa,EAAE,CAAC,SAASK,IAAI,OAAO,IAAIC,MAAM,0CAA0C,CAAC,SAASC,EAAEzE,GAAG,OAAO,IAAI0E,GAAGvC,KAAKnC,GAAG2E,OAAO,CAAC,SAASC,EAAE5E,GAAG,IAAIC,EAAEkC,KAAK,OAAO,IAAIlC,EAAE4E,EAAE7E,GAAG,SAASE,EAAEE,GAAG,IAAI,IAAIE,EAAEN,EAAEmE,OAAOzD,EAAE,EAAEA,EAAEJ,EAAEI,IAAIT,EAAE2D,QAAQ5D,EAAEU,IAAImD,KAAK3D,EAAEE,EAAE,EAAE,SAASJ,EAAEC,GAAG,OAAOA,EAAE,IAAI+C,UAAU,mCAAmC,EAAE,CAAC,SAAS8B,EAAE9E,GAAG,IAAWE,EAAE,IAAPiC,KAAaE,GAAG,OAAOiB,EAAEpD,EAAEF,GAAGE,CAAC,CAAC,SAAS6E,IAAI,MAAM,IAAI/B,UAAU,qFAAqF,CAAC,SAASgC,IAAI,MAAM,IAAIhC,UAAU,wHAAwH,CAAC,SAASiC,IAAI,IAAIjF,OAAE,EAAO,QAAG,IAAoBkF,EAAOlF,EAAEkF,OAAY,GAAG,oBAAoBC,KAAKnF,EAAEmF,UAAU,IAAInF,EAAE8B,SAAS,cAATA,EAAyB,CAAC,MAAM7B,GAAG,MAAM,IAAIuE,MAAM,2EAA2E,CAAC,IAAItE,EAAEF,EAAEoF,QAAQ,GAAGlF,EAAE,CAAC,IAAIE,EAAE,KAAK,IAAIA,EAAEiF,OAAOC,UAAUC,SAASpC,KAAKjD,EAAE0D,UAAU,CAAC,MAAM3D,GAAE,CAAE,GAAG,qBAAqBG,IAAIF,EAAEsF,KAAK,MAAM,CAACxF,EAAEoF,QAAQK,EAAE,CAAC,IAAIC,OAAE,EAAOA,EAAEC,MAAMC,QAAQD,MAAMC,QAAQ,SAAS5F,GAAG,MAAM,mBAAmBqF,OAAOC,UAAUC,SAASpC,KAAKnD,EAAE,EAAE,IAAI6E,EAAEa,EAAE/D,EAAE,EAAEhB,OAAE,EAAOR,OAAE,EAAOE,EAAE,SAASL,EAAEC,GAAG2B,EAAED,GAAG3B,EAAE4B,EAAED,EAAE,GAAG1B,EAAO,KAAL0B,GAAG,KAAUxB,EAAEA,EAAEM,GAAGoF,IAAI,EAAEC,EAAE,oBAAoBC,OAAOA,YAAO,EAAOC,EAAEF,GAAG,CAAA,EAAGhF,EAAEkF,EAAEC,kBAAkBD,EAAEE,uBAAuBC,EAAE,oBAAoBhB,MAAM,oBAAoB5E,SAAS,qBAAqB,CAAA,EAAGgF,SAASpC,KAAK5C,SAAS6F,EAAE,oBAAoBC,mBAAmB,oBAAoBC,eAAe,oBAAoBjF,eAAeO,EAAE,IAAI+D,MAAM,KAAKE,OAAE,EAAOA,EAAEM,EAAE7F,IAAIQ,EAAED,IAAIuF,EAAEhF,SAAI,IAAS0E,EAA8BjE,IAAIjB,IAAI,IAAI0B,EAAEiE,KAAKC,SAASjB,SAAS,IAAIkB,UAAU,GAAGxC,OAAE,EAAOR,EAAE,EAAEC,GAAE,EAAEY,GAAG,EAAEI,GAAG,WAAW,SAAS1E,EAAEA,EAAEC,GAAGkC,KAAKuE,qBAAqB1G,EAAEmC,KAAKwC,QAAQ,IAAI3E,EAAEqC,GAAGF,KAAKwC,QAAQrC,IAAIC,EAAEJ,KAAKwC,SAASE,EAAE5E,IAAIkC,KAAKgC,OAAOlE,EAAEkE,OAAOhC,KAAKwE,WAAW1G,EAAEkE,OAAOhC,KAAKQ,QAAQ,IAAIgD,MAAMxD,KAAKgC,QAAQ,IAAIhC,KAAKgC,OAAOd,EAAElB,KAAKwC,QAAQxC,KAAKQ,UAAUR,KAAKgC,OAAOhC,KAAKgC,QAAQ,EAAEhC,KAAKyE,WAAW3G,GAAG,IAAIkC,KAAKwE,YAAYtD,EAAElB,KAAKwC,QAAQxC,KAAKQ,WAAWW,EAAEnB,KAAKwC,QAAQJ,IAAI,CAAC,OAAOvE,EAAEsF,UAAUsB,WAAW,SAAS5G,GAAG,IAAI,IAAIC,EAAE,EAAEkC,KAAKK,SAASyB,GAAGhE,EAAED,EAAEmE,OAAOlE,IAAIkC,KAAK0E,WAAW7G,EAAEC,GAAGA,EAAE,EAAED,EAAEsF,UAAUuB,WAAW,SAAS7G,EAAEC,GAAG,IAAIC,EAAEiC,KAAKuE,qBAAqBtG,EAAEF,EAAE0D,QAAQ,GAAGxD,IAAIyC,EAAE,CAAC,IAAIvC,OAAE,EAAOI,OAAE,EAAOG,GAAE,EAAG,IAAIP,EAAEN,EAAE6D,IAAI,CAAC,MAAMzC,GAAGP,GAAE,EAAGH,EAAEU,CAAC,CAAC,GAAGd,IAAI4B,GAAGlC,EAAEwC,SAASyB,EAAE9B,KAAK2E,WAAW9G,EAAEwC,OAAOvC,EAAED,EAAE2C,cAAc,GAAG,mBAAmBrC,EAAE6B,KAAKwE,aAAaxE,KAAKQ,QAAQ1C,GAAGD,OAAO,GAAGE,IAAIuF,GAAG,CAAC,IAAI7E,EAAE,IAAIV,EAAEmC,GAAGxB,EAAEyC,EAAE1C,EAAEF,GAAGiD,EAAE/C,EAAEZ,EAAEM,GAAG6B,KAAK4E,cAAcnG,EAAEX,EAAE,MAAMkC,KAAK4E,cAAc,IAAI7G,GAAE,SAASD,GAAG,OAAOA,EAAED,EAAE,IAAGC,EAAE,MAAMkC,KAAK4E,cAAc3G,EAAEJ,GAAGC,EAAE,EAAED,EAAEsF,UAAUwB,WAAW,SAAS9G,EAAEC,EAAEC,GAAG,IAAIE,EAAE+B,KAAKwC,QAAQvE,EAAEoC,SAASyB,IAAI9B,KAAKwE,aAAa3G,IAAI0D,GAAEJ,EAAElD,EAAEF,GAAGiC,KAAKQ,QAAQ1C,GAAGC,GAAG,IAAIiC,KAAKwE,YAAYtD,EAAEjD,EAAE+B,KAAKQ,QAAQ,EAAE3C,EAAEsF,UAAUyB,cAAc,SAAS/G,EAAEC,GAAG,IAAIC,EAAEiC,KAAKS,EAAE5C,OAAE,GAAO,SAASA,GAAG,OAAOE,EAAE4G,WAAWrD,EAAExD,EAAED,EAAE,IAAE,SAASA,GAAG,OAAOE,EAAE4G,WAAWpD,GAAEzD,EAAED,EAAE,GAAE,EAAEA,CAAC,CAArsC,GAAysCyF,GAAG,WAAW,SAASzF,EAAEC,GAAGkC,KAAKG,GAAG+B,IAAIlC,KAAKQ,QAAQR,KAAKK,YAAO,EAAOL,KAAK+B,aAAa,GAAG7B,IAAIpC,IAAI,mBAAmBA,GAAG8E,IAAI5C,gBAAgBnC,EAAEoE,EAAEjC,KAAKlC,GAAG+E,IAAI,CAAC,OAAOhF,EAAEsF,UAAiB,MAAE,SAAStF,GAAG,OAAOmC,KAAK0B,KAAK,KAAK7D,EAAE,EAAEA,EAAEsF,UAAmB,QAAE,SAAStF,GAAG,IAAIE,EAAEiC,KAAK/B,EAAEF,EAAEkC,YAAY,OAAOnC,EAAED,GAAGE,EAAE2D,MAAK,SAAS5D,GAAG,OAAOG,EAAEwD,QAAQ5D,KAAK6D,MAAK,WAAW,OAAO5D,CAAC,GAAE,IAAE,SAASA,GAAG,OAAOG,EAAEwD,QAAQ5D,KAAK6D,MAAK,WAAW,MAAM5D,CAAC,GAAE,IAAGC,EAAE2D,KAAK7D,EAAEA,EAAE,EAAEA,CAAC,CAA1b,GAA8b,OAAOyF,GAAGH,UAAUzB,KAAK3B,EAAEuD,GAAGuB,IAAIvC,EAAEgB,GAAGwB,KAAKrC,EAAEa,GAAG7B,QAAQf,EAAE4C,GAAGyB,OAAOpC,EAAEW,GAAG0B,cAAcjH,EAAEuF,GAAG2B,SAAShH,EAAEqF,GAAG4B,MAAMhH,EAAEoF,GAAG6B,SAASrC,EAAEQ,GAAGL,QAAQK,GAAGA,EAAE,CAA9vMxF,8BCkBvD,SAAUsH,EAAUC,GAC5C,IAAIC,EAAYtF,KAChBA,KAAKuF,IAAM,KAEX,IAAI9D,EAAU,aACVsD,EAAS,aAkMb,YA/LuB,IAAZ9B,EAAAA,UAA4E,IAAjDA,UAAQG,WAAWoC,QAAQ,mBAC7DxF,KAAKyF,MAAQ,IAAIxC,EAAOA,SAAC,SAAUyC,EAAKC,GACpClE,EAAUiE,EACVX,EAASY,CACrB,KAGI3F,KAAK4F,KAAO,WACR,IAAIC,EAAU,IAAIC,eAElBD,EAAQE,KAAK,MAAOX,EAASY,cAAgB,WAAaZ,EAASa,MAAQ,mCAC3EJ,EAAQK,mBAAqB,WACC,GAAtBL,EAAQM,aACc,KAAlBN,EAAQO,QACRd,EAAUe,OAASC,KAAKC,MAAMV,EAAQW,cACtC/E,MAEAgF,QAAQC,MAAM,+CACd3B,KAGX,EAEDc,EAAQc,KAAK,KACrB,EAQI3G,KAAK4G,UAAY,SAAUC,GA0DvB,OAzDA7G,KAAK0B,KAAO,SAAUoF,EAASC,EAAQC,GACnC,GAAIH,GAAwBA,EAAqBI,OAAQ,CACrD,IAAIpB,EAAU,IAAIC,eAElBD,EAAQE,KAAK,OAAQT,EAAUe,OAAOa,gBAAgB,GACtDrB,EAAQsB,iBAAiB,eAAgB,qCACzCtB,EAAQsB,iBAAiB,gBAAiB,UAAY/B,EAASgC,OAE/DvB,EAAQK,mBAAqB,WACzB,GAA0B,GAAtBL,EAAQM,WAAiB,CACzB,IAAIC,EAASP,EAAQO,OAErB,GAAIA,GAAU,KAAOA,EAAS,IAAK,CAC/B,IAAIb,EAAMe,KAAKC,MAAMV,EAAQW,cAAca,aAC3C/B,EAAUC,IAAMA,EAChBuB,EAAQvB,EACpC,MAA6C,KAAVa,EACHW,EACAA,IAEAN,QAAQC,MAAM,mDAGdM,EACAA,IAEAP,QAAQC,MAAM,mDAGzB,CACrB,EAEgB,IAAIY,EAAS,oEAAsElC,EAASmC,SAAW,WAAaV,EAAqBI,OAE/FO,MAAtCX,EAAqBY,gBACrBH,GAAU,mBAAqBT,EAAqBY,eAGxD,IAAIC,EAAWb,EAAqBa,SAEhCA,IACIA,EAASC,8BACTL,GAAU,mCAAqCI,EAASC,6BAExDD,EAASE,2BACTN,GAAU,+BAAiCI,EAASE,2BAIxDtC,EAAUC,MAAyDiC,MAAjDX,EAAqBgB,0BAAyChB,EAAqBgB,4BACrGP,GAAU,QAAUhC,EAAUC,KAGlCM,EAAQc,KAAKW,EAChB,CACb,EAEetH,IACf,EAKIA,KAAK8H,YAAc,SAAUC,EAAkBlB,GA0F3C,OAzFA7G,KAAK0B,KAAO,SAAUoF,EAASC,EAAQC,GACnC,IAAInB,EAAU,IAAIC,eAElBD,EAAQE,KAAK,OAAQT,EAAUe,OAAOa,gBAAgB,GACtDrB,EAAQsB,iBAAiB,eAAgB,qCACzCtB,EAAQsB,iBAAiB,gBAAiB,UAAY/B,EAASgC,OAE/DvB,EAAQK,mBAAqB,WACzB,GAA0B,GAAtBL,EAAQM,WAAiB,CACzB,IAAIC,EAASP,EAAQO,OAErB,GAAIA,GAAU,KAAOA,EAAS,IAAK,CAC/B,IAAIb,EAAMe,KAAKC,MAAMV,EAAQW,cAAca,aAC3C/B,EAAUC,IAAMA,EAChBuB,EAAQvB,EAChC,MAAyC,KAAVa,EACHW,EACAA,IAEAN,QAAQC,MAAM,mDAGdM,EACAA,IAEAP,QAAQC,MAAM,mDAGzB,CACjB,EAEiBG,IACDA,EAAuB,CAAA,GAG3B,IAAIS,EAAS,oEAAsElC,EAASmC,SAExFV,EAAqBmB,aACrBV,GAAU,gBAAkBT,EAAqBmB,WAE7CnB,EAAqBoB,mBACrBX,GAAU,uBAAyBT,EAAqBoB,mBAIhEX,GAAU,aAAeS,EAEzB,IAAIG,EAAcrB,EAAqBqB,YAElCA,IACDA,EAAc,IAGlB,IAAK,IAAI3J,EAAI,EAAGA,EAAI2J,EAAYlG,OAAQzD,IAAK,CACzC,IAAI4J,EAAWD,EAAY3J,GACvB6J,EAAaD,EAASE,GAE1B,GAAIF,EAASG,QAAUH,EAASG,OAAOtG,OAAS,EAAG,CAC/CoG,GAAc,IACd,IAAK,IAAI3H,EAAI,EAAGA,EAAI0H,EAASG,OAAOtG,OAAQvB,IAAK,CAC7C,IAAI8H,EAAQJ,EAASG,OAAO7H,GACxB2H,EAAW5C,QAAQ,MAAQ4C,EAAWpG,OAAS,IAC/CoG,GAAc,KAElBA,GAAcG,CACjB,CACJ,CAEDjB,GAAU,eAAiBc,CAC9B,CAED,IAAIV,EAAWb,EAAqBa,SAEhCA,IACIA,EAASC,8BACTL,GAAU,mCAAqCI,EAASC,6BAExDD,EAASE,2BACTN,GAAU,+BAAiCI,EAASE,2BAIxDtC,EAAUC,MACV+B,GAAU,QAAUhC,EAAUC,KAGlCM,EAAQc,KAAKW,EACzB,EAEetH,IACf,EAEIA,KAAK4F,KAAK5F,MAEHA,IACX", "x_google_ignoreList": [0]}