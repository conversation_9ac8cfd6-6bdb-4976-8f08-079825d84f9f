<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>org.springframework.boot</groupId>
		<artifactId>spring-boot-starter-parent</artifactId>
		<version>3.4.1</version>
		<relativePath /> <!-- lookup parent from repository -->
	</parent>
	<groupId>dz.sonatrach.weblqs</groupId>
	<artifactId>mayaa_back</artifactId>
	<version>0.1</version>
	<packaging>war</packaging>
	<name>mayaa-back</name>
	<description>mayaa backend</description>
	<properties>
		<java.version>17</java.version>
	</properties>
	<dependencyManagement>
		<dependencies>
			<dependency>
				<groupId>io.micrometer</groupId>
				<artifactId>micrometer-registry-prometheus</artifactId>
				<version>1.14.2</version>
			</dependency>
		</dependencies>
	</dependencyManagement>
	<dependencies>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-data-jpa</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-web</artifactId>
			<exclusions>
				<exclusion>
					<groupId>org.yaml</groupId>
					<artifactId>snakeyaml</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-validation</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-websocket</artifactId>
		</dependency>
		
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-oauth2-resource-server</artifactId>
		</dependency>
		<dependency>
			<groupId>org.keycloak</groupId>
			<artifactId>keycloak-admin-client</artifactId>
			<version>24.0.5</version>
		</dependency>
		<dependency>
			<groupId>com.oracle.database.jdbc</groupId>
			<artifactId>ojdbc8</artifactId>			
		</dependency>


		<dependency>
			<groupId>org.projectlombok</groupId>
			<artifactId>lombok</artifactId>
			<!--			<optional>true</optional>-->
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-devtools</artifactId>
		</dependency>

		<dependency>
			<groupId>com.google.zxing</groupId>
			<artifactId>javase</artifactId>
			<version>3.5.3</version>
		</dependency>
		<!--Fin codeQR -->
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-tomcat</artifactId>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>com.github.gavlyukovskiy</groupId>
			<artifactId>datasource-proxy-spring-boot-starter</artifactId>
			<version>1.9.2</version>
		</dependency>

		<dependency>
			<groupId>io.sentry</groupId>
			<artifactId>sentry-spring-boot-starter-jakarta</artifactId>
			<version>7.19.1</version>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-actuator</artifactId>
		</dependency>

		<dependency>
			<groupId>io.micrometer</groupId>
			<artifactId>micrometer-registry-prometheus</artifactId>

			<scope>runtime</scope>
		</dependency>
		<dependency>
			<groupId>io.github.perplexhub</groupId>
			<artifactId>rsql-jpa-spring-boot-starter</artifactId>
			<version>6.0.23</version>
		</dependency>
	</dependencies>
	<build>
		<plugins>
			<plugin>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-maven-plugin</artifactId>
				<!--				<configuration>-->
				<!--					<excludes>-->
				<!--						<exclude>-->
				<!--							<groupId>org.projectlombok</groupId>-->
				<!--							<artifactId>lombok</artifactId>-->
				<!--						</exclude>-->
				<!--					</excludes>-->
				<!--				</configuration>-->
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-compiler-plugin</artifactId>
			</plugin>
			
			<plugin>
				<groupId>io.sentry</groupId>
				<artifactId>sentry-maven-plugin</artifactId>
				<version>0.0.8</version>
				<extensions>true</extensions>
				<configuration>
					<debugSentryCli>true</debugSentryCli>
					<skip>false</skip>
					<skipSourceBundle>false</skipSourceBundle>
					<skipAutoInstall>false</skipAutoInstall>
					<org>sentry</org>
					<url>https://orndevapm.corp.sonatrach.dz:8443</url>
					<project>mayaa-back</project>

					<authToken>
						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
					</authToken>
				</configuration>
				<executions>
					<execution>
						<goals>
							<goal>uploadSourceBundle</goal>
						</goals>
					</execution>
				</executions>
			</plugin>
			<plugin>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-maven-plugin</artifactId>
				<configuration>
					<jvmArguments>
						-Duser.timezone=Europe/Paris
					</jvmArguments>
				</configuration>
			</plugin>
		</plugins>
	</build>
	<profiles>
		<profile>
			<id>local</id>
			<properties>
				<spring.profiles.active>local</spring.profiles.active>
			</properties>
		</profile>
		<profile>
			<id>dev</id>
			<activation>
				<activeByDefault>true</activeByDefault>
			</activation>
			<properties>
				<spring.profiles.active>dev</spring.profiles.active>
			</properties>
			<build>
				<finalName>mayaa_back</finalName> <!-- Nom du fichier WAR pour
				le profil kdev -->
			</build>
		</profile>
		<profile>
			<id>qualif</id>
			<properties>
				<spring.profiles.active>qualif</spring.profiles.active>
			</properties>
			<build>
				<finalName>mayaa_back</finalName> <!-- Nom du fichier WAR pour
				le profil kdev -->
			</build>
		</profile>
		<profile>
			<id>prod</id>
			<properties>
				<spring.profiles.active>prod</spring.profiles.active>
			</properties>
			<build>
				<finalName>mayaa_back</finalName>
			</build>
		</profile>

	</profiles>
	<!--<packaging>war</packaging>-->
</project>
