package dz.sonatrach.weblqs.mayaaback.model;

import com.fasterxml.jackson.annotation.JsonView;
import dz.sonatrach.weblqs.mayaaback.views.View;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;

import jakarta.persistence.Table;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * Entité pour la vue CONSOMMATION_ENERGETIQUE
 * Représente les données de consommation énergétique (Sonelgaz et Kahrama)
 */
@Entity
@Table(name = "CONSOMMATION_ENERGETIQUE")
@EqualsAndHashCode(onlyExplicitlyIncluded = true)
@ToString
public class ConsommationEnergetique implements Serializable {
    private static final long serialVersionUID = 1L;

    @Id
    @Column(name = "ID")
    @JsonView(View.basic.class)
    private Long id;

    @Column(name = "UNITE", length = 10)
    @JsonView(View.basic.class)
    private String unite;

    @Column(name = "MOIS")
    @JsonView(View.basic.class)
    private LocalDate mois;

    @Column(name = "CE_ELECTRICITE_SONALGAZ")
    @JsonView(View.basic.class)
    private BigDecimal ceElectriciteSonalgaz;

    @Column(name = "CE_KAHRAMA")
    @JsonView(View.basic.class)
    private BigDecimal ceKahrama;

    // Constructeurs
    public ConsommationEnergetique() {}

    public ConsommationEnergetique(Long id, String unite, LocalDate mois, 
                                 BigDecimal ceElectriciteSonalgaz, BigDecimal ceKahrama) {
        this.id = id;
        this.unite = unite;
        this.mois = mois;
        this.ceElectriciteSonalgaz = ceElectriciteSonalgaz;
        this.ceKahrama = ceKahrama;
    }

    // Getters et Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getUnite() {
        return unite;
    }

    public void setUnite(String unite) {
        this.unite = unite;
    }

    public LocalDate getMois() {
        return mois;
    }

    public void setMois(LocalDate mois) {
        this.mois = mois;
    }

    public BigDecimal getCeElectriciteSonalgaz() {
        return ceElectriciteSonalgaz;
    }

    public void setCeElectriciteSonalgaz(BigDecimal ceElectriciteSonalgaz) {
        this.ceElectriciteSonalgaz = ceElectriciteSonalgaz;
    }

    public BigDecimal getCeKahrama() {
        return ceKahrama;
    }

    public void setCeKahrama(BigDecimal ceKahrama) {
        this.ceKahrama = ceKahrama;
    }
}
